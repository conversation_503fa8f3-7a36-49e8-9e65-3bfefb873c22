{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "check-all": "npm run type-check && npm run lint && npm run format:check", "types:generate": "node scripts/generate-types.js", "types:generate-remote": "supabase gen types typescript --project-id evauqytvhvjuryhhfjoy --schema public > src/types/supabase.ts", "types:generate-local": "supabase gen types typescript --local --schema public > src/types/supabase.ts", "types:watch": "npm run types:generate && echo 'Types generated successfully!'", "migrate:example-data": "node scripts/migrate-example-data.js", "migrate:apply": "node scripts/apply-migration.js", "test:business-scraper": "tsx scripts/test-business-scraper.ts", "test:scrapers": "tsx scripts/test-all-scrapers.ts", "test:implementation": "tsx scripts/test-implementation.ts", "test:reviews-scraper": "tsx scripts/test-existing-reviews.ts", "setup:demo": "npm run migrate:example-data && npm run types:generate", "postinstall": "npm run types:generate"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.4", "@ai-sdk/google": "^2.0.6", "@ai-sdk/openai": "^2.0.14", "@azure/ai-text-analytics": "^5.1.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.0", "ai": "^5.0.14", "apify-client": "^2.15.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.539.0", "next": "15.4.6", "next-intl": "^4.3.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tsx": "^4.20.4", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "supabase": "^2.34.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}