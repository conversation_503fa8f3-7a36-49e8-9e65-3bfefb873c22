# Minimal Embeddable Chatbot Widget

A lightweight React + TypeScript chatbot widget that can be embedded into any webpage with Shadow DOM isolation.

## Features

- ✅ Built with React 18 + TypeScript
- ✅ Shadow DOM isolation (host page CSS won't interfere)
- ✅ Single JavaScript file (~191KB, ~60KB gzipped)
- ✅ Simple keyword-based responses
- ✅ Responsive design with minimal styling
- ✅ No external dependencies required
- ✅ Auto-scroll to new messages
- ✅ Typing indicator
- ✅ Enter key support

## Usage

### Simple Integration

Add these two lines to any webpage:

```html
<div id="chatbot-widget"></div>
<script src="chatbot-widget.js"></script>
```

### Example HTML

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Website</title>
</head>
<body>
    <h1>Welcome to my website</h1>
    <p>This is my content...</p>
    
    <!-- Chatbot widget -->
    <div id="chatbot-widget"></div>
    <script src="chatbot-widget.js"></script>
</body>
</html>
```

### Manual Initialization

If you need more control:

```html
<div id="my-custom-chatbot"></div>
<script src="chatbot-widget.js"></script>
<script>
    // Wait for the widget to load, then initialize manually
    setTimeout(() => {
        // Change the target element ID
        document.getElementById('my-custom-chatbot').id = 'chatbot-widget';
        ChatbotWidget.initializeChatbot();
    }, 100);
</script>
```

## Widget Appearance

The chatbot appears as a fixed floating widget in the bottom-right corner of the page:

- **Size**: 350px × 500px
- **Position**: Fixed, bottom-right corner (20px margins)
- **Theme**: Clean blue theme with rounded corners
- **Shadow**: Subtle drop shadow for depth
- **Z-index**: 10000 (appears above most content)

## Bot Responses

The chatbot includes simple keyword-based responses:

- **Greetings**: "hello", "hi" → personalized greeting
- **Help**: "help" → explanation of capabilities
- **Status**: "how are you" → friendly response
- **Goodbye**: "bye", "goodbye" → farewell message
- **Default**: Random canned responses for other inputs

## Development

### Prerequisites

- Node.js 18+
- npm

### Setup

```bash
npm install
```

### Development Mode

```bash
npm run dev
```

### Build

```bash
npm run build
```

This creates `dist/chatbot-widget.js` - the single file needed for embedding.

### Testing

Open `test.html` in a browser to see the widget in action with style isolation testing.

### Code Quality

```bash
npm run lint     # ESLint
npm run format   # Prettier
```

## Browser Support

- Modern browsers with Shadow DOM support (Chrome 53+, Firefox 63+, Safari 10.1+)
- ES6+ features used (async/await, arrow functions, etc.)

## File Structure

```
chatbot-widget/
├── src/
│   ├── main.tsx          # Shadow DOM mounting logic
│   ├── App.tsx           # Chat UI component
│   └── vite-env.d.ts     # TypeScript declarations
├── dist/
│   └── chatbot-widget.js # Built widget (single file)
├── test.html             # Test page for embedding
├── package.json          # Dependencies and scripts
├── vite.config.ts        # Build configuration
├── tsconfig.json         # TypeScript configuration
├── .eslintrc.json        # ESLint configuration
└── .prettierrc           # Prettier configuration
```

## Customization

To customize the widget:

1. Edit styles in `src/main.tsx` (CSS-in-JS)
2. Modify responses in `src/App.tsx` (`generateBotResponse` function)
3. Rebuild: `npm run build`

## Shadow DOM Isolation

The widget uses Shadow DOM to ensure complete CSS and JS isolation:

- Host page styles cannot affect the widget
- Widget styles cannot leak to the host page
- No naming conflicts or interference
- Self-contained styling and behavior