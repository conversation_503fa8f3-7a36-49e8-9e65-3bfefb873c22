<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chatbot Widget Test - New Flow</title>
    <style>
      /* Host page styles to test isolation */
      body {
        font-family: serif;
        background: #f0f0f0;
        margin: 0;
        padding: 20px;
        line-height: 1.6;
      }

      h1 {
        color: red;
        font-size: 48px;
      }

      h2 {
        color: #333;
        font-size: 24px;
        margin-top: 30px;
      }

      p {
        color: blue;
        font-size: 20px;
      }

      .test-section {
        background: white;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
        border: 2px solid #ddd;
      }

      .test-button {
        background: #28a745;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin: 5px;
      }

      /* These styles should NOT affect the chatbot */
      .chatbot-container {
        background: red !important;
        color: yellow !important;
      }

      button {
        background: green !important;
        color: white !important;
        font-size: 24px !important;
      }

      .code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 10px 0;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>Chatbot Widget Test - New Flow</h1>
    <p>
      This page demonstrates the new flow-based chatbot architecture with
      button-triggered initialization.
    </p>

    <div class="test-section">
      <h2>🎯 Flow Overview</h2>
      <ol>
        <li><strong>Script Injection:</strong> Widget loads via script tag</li>
        <li><strong>Button Render:</strong> Only a button appears initially</li>
        <li><strong>Session Check:</strong> On click, checks for existing session</li>
        <li><strong>Initialize:</strong> Creates new or reuses existing session</li>
        <li><strong>Chatbot Display:</strong> Shows full chatbot interface</li>
      </ol>
    </div>

    <div class="test-section">
      <h2>🔧 Manual Initialization Tests</h2>
      <p>Test different initialization parameters:</p>
      
      <button class="test-button" onclick="testBasicInit()">
        Basic Initialization
      </button>
      
      <button class="test-button" onclick="testWithUserInfo()">
        With User Info
      </button>
      
      <button class="test-button" onclick="testWithTopic()">
        With Topic
      </button>
      
      <button class="test-button" onclick="testFullParams()">
        Full Parameters
      </button>
      
      <button class="test-button" onclick="testDifferentPosition()">
        Different Position
      </button>
    </div>

    <div class="test-section">
      <h2>💡 Expected Behavior</h2>
      <ul>
        <li>Initially, only a floating button should appear (bottom-right by default)</li>
        <li>Clicking the button opens the chatbot interface</li>
        <li>Personalized greeting based on initialization parameters</li>
        <li>Session persistence - refresh page and existing session continues</li>
        <li>Context-aware responses based on user info and topic</li>
        <li>Clean blue theme (not affected by host page's conflicting styles)</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>📋 API Examples</h2>
      <p>Manually initialize with custom parameters:</p>
      <div class="code-block">
// Basic initialization
ChatbotWidget.initialize();

// With user information
ChatbotWidget.initialize({
  authToken: "user-token-123",
  userInfo: { name: "John Doe", email: "<EMAIL>" },
  topic: "Customer Support"
});

// Different position
ChatbotWidget.initialize({
  position: "left-bottom" // or "right-top", "left-top"
});
      </div>
    </div>

    <!-- Target element for the chatbot widget -->
    <div id="chatbot-widget"></div>

    <!-- Load the chatbot widget -->
    <!-- Use built version in production, dev version in development -->
    <script>
      // Check if we're in development mode (port 5173)
      const isDev = window.location.port === '5173';
      
      if (isDev) {
        // Development mode - load source directly
        const script = document.createElement('script');
        script.type = 'module';
        script.src = '/src/main.tsx';
        document.head.appendChild(script);
      } else {
        // Production mode - load built file
        const script = document.createElement('script');
        script.src = 'dist/chatbot-widget.js';
        document.head.appendChild(script);
      }
    </script>

    <script>
      // Test functions for manual initialization
      function testBasicInit() {
        console.log('Testing basic initialization...');
        ChatbotWidget.initialize();
        console.log('Basic init called');
      }

      function testWithUserInfo() {
        console.log('Testing with user info...');
        ChatbotWidget.initialize({
          userInfo: { 
            name: "Alice Johnson", 
            email: "<EMAIL>",
            id: "user-123"
          }
        });
        console.log('User info init called');
      }

      function testWithTopic() {
        console.log('Testing with topic...');
        ChatbotWidget.initialize({
          topic: "Technical Support"
        });
        console.log('Topic init called');
      }

      function testFullParams() {
        console.log('Testing with full params...');
        ChatbotWidget.initialize({
          authToken: "jwt-token-abc123",
          userInfo: { 
            name: "Bob Smith", 
            email: "<EMAIL>",
            id: "employee-456"
          },
          topic: "HR Questions"
        });
        console.log('Full params init called');
      }

      function testDifferentPosition() {
        console.log('Testing different position...');
        ChatbotWidget.initialize({
          position: "left-bottom",
          userInfo: { name: "Charlie" },
          topic: "Sales Inquiry"
        });
        console.log('Different position init called');
      }

      // Log session info for debugging
      setTimeout(() => {
        console.log("Window.ChatbotWidget:", window.ChatbotWidget);
      }, 1000);
    </script>
  </body>
</html>