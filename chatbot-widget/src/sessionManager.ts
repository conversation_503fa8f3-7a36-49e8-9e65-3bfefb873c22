import { type ChatbotSession, type ChatbotInitParams, INITIAL_MESSAGE } from './chatbotConstants';

const SESSION_STORAGE_KEY = 'chatbot-session';
const SESSION_EXPIRY_HOURS = 24; // Session expires after 24 hours

export class SessionManager {
  private static instance: SessionManager;
  private currentSession: ChatbotSession | null = null;

  private constructor() {}

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Check if a valid session exists
   */
  public hasValidSession(): boolean {
    const session = this.loadSession();
    if (!session) return false;

    // Check if session has expired
    const now = new Date();
    const sessionAge = now.getTime() - new Date(session.createdAt).getTime();
    const maxAge = SESSION_EXPIRY_HOURS * 60 * 60 * 1000; // Convert hours to ms

    if (sessionAge > maxAge) {
      this.clearSession();
      return false;
    }

    this.currentSession = session;
    return true;
  }

  /**
   * Create a new session with initialization parameters
   */
  public createSession(params: ChatbotInitParams): ChatbotSession {
    const session: ChatbotSession = {
      id: this.generateSessionId(),
      authToken: params.authToken,
      userInfo: params.userInfo,
      topic: params.topic,
      messages: [this.createInitialMessage(params)],
      createdAt: new Date(),
    };

    this.currentSession = session;
    this.saveSession(session);
    return session;
  }

  /**
   * Get the current session
   */
  public getCurrentSession(): ChatbotSession | null {
    return this.currentSession;
  }

  /**
   * Update the current session
   */
  public updateSession(session: ChatbotSession): void {
    this.currentSession = session;
    this.saveSession(session);
  }

  /**
   * Clear the current session
   */
  public clearSession(): void {
    this.currentSession = null;
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(SESSION_STORAGE_KEY);
    }
  }

  /**
   * Load session from localStorage
   */
  private loadSession(): ChatbotSession | null {
    if (typeof localStorage === 'undefined') return null;

    try {
      const sessionData = localStorage.getItem(SESSION_STORAGE_KEY);
      if (!sessionData) return null;

      const session = JSON.parse(sessionData) as ChatbotSession;
      // Convert date strings back to Date objects
      session.createdAt = new Date(session.createdAt);
      session.messages = session.messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));

      return session;
    } catch (error) {
      console.error('Failed to load chatbot session:', error);
      return null;
    }
  }

  /**
   * Save session to localStorage
   */
  private saveSession(session: ChatbotSession): void {
    if (typeof localStorage === 'undefined') return;

    try {
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to save chatbot session:', error);
    }
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `chatbot-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Create initial message based on initialization parameters
   */
  private createInitialMessage(params: ChatbotInitParams): typeof INITIAL_MESSAGE {
    let greeting = "Hello! I'm a minimal chatbot.";
    
    if (params.userInfo?.name) {
      greeting = `Hello ${params.userInfo.name}! I'm a minimal chatbot.`;
    }

    if (params.topic) {
      greeting += ` I'm here to help you with ${params.topic}.`;
    } else {
      greeting += " How can I help you today?";
    }

    return {
      ...INITIAL_MESSAGE,
      text: greeting,
    };
  }
}