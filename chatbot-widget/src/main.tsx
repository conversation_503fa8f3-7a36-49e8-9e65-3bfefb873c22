import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ChatbotWidget } from './ChatbotWidget';
import { type ChatbotInitParams } from './chatbotConstants';

// CSS styles to inject into Shadow DOM
const styles = `
  .chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    z-index: 10000;
  }

  .chatbot-overlay {
    position: fixed;
    bottom: 90px;
    right: 20px;
    z-index: 10000;
  }

  .chatbot-header {
    background: #4a90e2;
    color: white;
    padding: 16px;
    border-radius: 12px 12px 0 0;
    font-weight: 600;
    font-size: 16px;
  }

  .chatbot-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .message {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
  }

  .message.user {
    background: #4a90e2;
    color: white;
    align-self: flex-end;
  }

  .message.bot {
    background: #f1f3f5;
    color: #333;
    align-self: flex-start;
  }

  .chatbot-input-container {
    display: flex;
    padding: 16px;
    border-top: 1px solid #e1e5e9;
    gap: 8px;
  }

  .chatbot-input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
  }

  .chatbot-input:focus {
    border-color: #4a90e2;
  }

  .chatbot-send-button {
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
  }

  .chatbot-send-button:hover {
    background: #3a7bd5;
  }

  .chatbot-send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .chatbot-trigger-button {
    position: fixed;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background-color: #4a90e2;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .chatbot-trigger-button:hover {
    background-color: #3a7bd5;
    transform: scale(1.05);
  }

  .chatbot-trigger-button.active {
    background-color: #3a7bd5;
    transform: scale(0.95);
  }
`;

// Global initialization parameters
let globalInitParams: ChatbotInitParams = {
  position: 'right-bottom'
};

let isWidgetInitialized = false;
let widgetRoot: ReturnType<typeof createRoot> | null = null;

// Function to initialize the chatbot widget
function initializeChatbot(params: ChatbotInitParams = {}) {
  // Merge with global params
  globalInitParams = { ...globalInitParams, ...params };
  
  if (isWidgetInitialized && widgetRoot) {
    // Widget already initialized, just re-render with new params
    widgetRoot.render(
      <StrictMode>
        <ChatbotWidget initParams={globalInitParams} />
      </StrictMode>
    );
    return;
  }
  
  // Find the target element
  const targetElement = document.getElementById('chatbot-widget');
  
  if (!targetElement) {
    console.error('Chatbot widget target element not found. Please add <div id="chatbot-widget"></div> to your HTML.');
    return;
  }

  // Check if shadow root already exists
  let shadowRoot = targetElement.shadowRoot;
  if (!shadowRoot) {
    // Create shadow DOM
    shadowRoot = targetElement.attachShadow({ mode: 'open' });
    
    // Create style element and inject CSS
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRoot.appendChild(styleElement);
    
    // Create container for React app
    const reactContainer = document.createElement('div');
    reactContainer.id = 'react-root';
    shadowRoot.appendChild(reactContainer);
  }

  // Get or create React root
  const reactContainer = shadowRoot.getElementById('react-root')!;
  if (!widgetRoot) {
    widgetRoot = createRoot(reactContainer);
  }
  
  // Render React app into shadow DOM
  widgetRoot.render(
    <StrictMode>
      <ChatbotWidget initParams={globalInitParams} />
    </StrictMode>
  );
  
  isWidgetInitialized = true;
}

// Auto-initialize with default params when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => initializeChatbot());
} else {
  initializeChatbot();
}

// Export for manual initialization with custom params
declare global {
  interface Window {
    ChatbotWidget: {
      initialize: (params?: ChatbotInitParams) => void;
    };
  }
}

window.ChatbotWidget = {
  initialize: initializeChatbot,
};

// Export for potential manual initialization
export { initializeChatbot };