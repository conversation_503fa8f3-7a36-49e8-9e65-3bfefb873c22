import { useState } from 'react';
import { ChatbotButton } from './ChatbotButton';
import { ChatbotProvider } from './ChatbotContext';
import { useChatbot } from './useChatbot';
import App from './App';
import { type ChatbotInitParams } from './chatbotConstants';

interface ChatbotWidgetProps {
  initParams: ChatbotInitParams;
}

function ChatbotContent({ initParams }: ChatbotWidgetProps) {
  const { isInitialized, initializeSession } = useChatbot();
  const [showChatbot, setShowChatbot] = useState(false);
  const position = initParams.position || 'right-bottom';

  const handleButtonClick = () => {
    if (!isInitialized) {
      initializeSession(initParams);
    }
    setShowChatbot(!showChatbot);
  };

  const getOverlayPositionStyle = (position: string) => {
    const styles: React.CSSProperties = {
      position: 'fixed',
      zIndex: 10000,
    };
    
    switch (position) {
      case 'left-bottom':
        styles.bottom = '90px';
        styles.left = '20px';
        break;
      case 'right-top':
        styles.top = '90px';
        styles.right = '20px';
        break;
      case 'left-top':
        styles.top = '90px';
        styles.left = '20px';
        break;
      case 'right-bottom':
      default:
        styles.bottom = '90px';
        styles.right = '20px';
        break;
    }
    
    return styles;
  };

  return (
    <>
      <ChatbotButton 
        position={position}
        onClick={handleButtonClick}
        isActive={showChatbot}
      />
      {showChatbot && isInitialized && (
        <div className="chatbot-overlay" style={getOverlayPositionStyle(position)}>
          <App />
        </div>
      )}
    </>
  );
}

export function ChatbotWidget({ initParams }: ChatbotWidgetProps) {
  return (
    <ChatbotProvider>
      <ChatbotContent initParams={initParams} />
    </ChatbotProvider>
  );
}