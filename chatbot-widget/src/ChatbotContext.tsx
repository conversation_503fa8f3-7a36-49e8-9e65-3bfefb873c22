import { useState, useCallback, useEffect, type ReactNode } from 'react';
import { type Message, CANNED_RESPONSES, type ChatbotSession, type ChatbotInitParams } from './chatbotConstants';
import { ChatbotContext } from './chatbotContextDefinition';
import { SessionManager } from './sessionManager';

export interface ChatbotContextType {
  messages: Message[];
  isTyping: boolean;
  session: ChatbotSession | null;
  isInitialized: boolean;
  sendMessage: (text: string) => Promise<void>;
  clearMessages: () => void;
  initializeSession: (params: ChatbotInitParams) => void;
}

interface ChatbotProviderProps {
  children: ReactNode;
  responses?: string[];
  typingDelay?: { min: number; max: number };
}

export function ChatbotProvider({ 
  children, 
  responses = CANNED_RESPONSES,
  typingDelay = { min: 1000, max: 2000 }
}: ChatbotProviderProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [session, setSession] = useState<ChatbotSession | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const sessionManager = SessionManager.getInstance();

  // Initialize session on component mount if one exists
  useEffect(() => {
    if (sessionManager.hasValidSession()) {
      const existingSession = sessionManager.getCurrentSession();
      if (existingSession) {
        setSession(existingSession);
        setMessages(existingSession.messages);
        setIsInitialized(true);
      }
    }
  }, [sessionManager]);

  const generateBotResponse = useCallback((userMessage: string, currentSession?: ChatbotSession): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Context-aware responses based on session data
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      const name = currentSession?.userInfo?.name;
      return name ? `Hello ${name}! Nice to see you again!` : 'Hello there! Nice to meet you!';
    }
    if (lowerMessage.includes('help')) {
      const topic = currentSession?.topic;
      return topic 
        ? `I'm here to help you with ${topic}! What specific questions do you have?`
        : "I'm here to help! I'm a simple chatbot that can have basic conversations with you.";
    }
    if (lowerMessage.includes('how are you')) {
      return "I'm doing great, thanks for asking! How are you today?";
    }
    if (lowerMessage.includes('bye') || lowerMessage.includes('goodbye')) {
      return 'Goodbye! Thanks for chatting with me. Have a great day!';
    }
    
    // Default to random canned response
    return responses[Math.floor(Math.random() * responses.length)];
  }, [responses]);

  const initializeSession = useCallback((params: ChatbotInitParams) => {
    let newSession: ChatbotSession;
    
    if (sessionManager.hasValidSession()) {
      // Reuse existing session but check if we need to update it with new params
      const existingSession = sessionManager.getCurrentSession()!;
      const hasNewParams = params.userInfo || params.topic || params.authToken;
      
      if (hasNewParams) {
        // Create fresh session with new params to get personalized greeting
        newSession = sessionManager.createSession(params);
      } else {
        newSession = existingSession;
      }
    } else {
      // Create new session
      newSession = sessionManager.createSession(params);
    }
    
    setSession(newSession);
    setMessages(newSession.messages);
    setIsInitialized(true);
  }, [sessionManager]);

  const sendMessage = useCallback(async (text: string) => {
    if (!text.trim() || !session) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    // Add user message to state and session
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setIsTyping(true);

    // Simulate typing delay
    const delay = typingDelay.min + Math.random() * (typingDelay.max - typingDelay.min);
    await new Promise(resolve => setTimeout(resolve, delay));

    // Generate and add bot response
    const botResponse: Message = {
      id: (Date.now() + 1).toString(),
      text: generateBotResponse(userMessage.text, session),
      isUser: false,
      timestamp: new Date(),
    };

    const finalMessages = [...updatedMessages, botResponse];
    setMessages(finalMessages);
    setIsTyping(false);

    // Update session with new messages
    const updatedSession = { ...session, messages: finalMessages };
    setSession(updatedSession);
    sessionManager.updateSession(updatedSession);
  }, [messages, session, generateBotResponse, typingDelay, sessionManager]);

  const clearMessages = useCallback(() => {
    if (session) {
      const clearedSession = { ...session, messages: [session.messages[0]] };
      setSession(clearedSession);
      setMessages([session.messages[0]]);
      sessionManager.updateSession(clearedSession);
    }
  }, [session, sessionManager]);

  const contextValue: ChatbotContextType = {
    messages,
    isTyping,
    session,
    isInitialized,
    sendMessage,
    clearMessages,
    initializeSession,
  };

  return (
    <ChatbotContext.Provider value={contextValue}>
      {children}
    </ChatbotContext.Provider>
  );
}

