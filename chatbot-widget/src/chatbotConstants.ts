export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export interface UserInfo {
  name?: string;
  email?: string;
  id?: string;
  [key: string]: unknown;
}

export interface ChatbotSession {
  id: string;
  authToken?: string;
  userInfo?: UserInfo;
  topic?: string;
  messages: Message[];
  createdAt: Date;
}

export type ButtonPosition = 'left-bottom' | 'right-bottom' | 'right-top' | 'left-top';

export interface ChatbotInitParams {
  authToken?: string;
  userInfo?: UserInfo;
  topic?: string;
  position?: ButtonPosition;
}

export const CANNED_RESPONSES = [
  "Hello! I'm a minimal chatbot. How can I help you today?",
  "That's interesting! Tell me more.",
  "I understand. Is there anything else you'd like to know?",
  "Thanks for chatting with me! I'm here to help.",
  "I'm a simple chatbot, but I'm happy to chat with you!",
];

export const INITIAL_MESSAGE: Message = {
  id: '1',
  text: "Hello! I'm a minimal chatbot. How can I help you today?",
  isUser: false,
  timestamp: new Date(),
};

export const BUTTON_POSITION_STYLES: Record<ButtonPosition, string> = {
  'left-bottom': 'bottom: 20px; left: 20px;',
  'right-bottom': 'bottom: 20px; right: 20px;',
  'right-top': 'top: 20px; right: 20px;',
  'left-top': 'top: 20px; left: 20px;',
};