import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    lib: {
      entry: './src/main.tsx',
      name: 'ChatbotWidget',
      fileName: 'chatbot-widget',
      formats: ['iife'], // Immediately Invoked Function Expression for embedding
    },
    rollupOptions: {
      output: {
        // Bundle everything into a single file
        inlineDynamicImports: true,
        manualChunks: undefined,
      },
    },
    // Ensure all dependencies are bundled
    commonjsOptions: {
      include: [/node_modules/],
    },
  },
  define: {
    // Define NODE_ENV for production build
    'process.env.NODE_ENV': JSON.stringify('production'),
  },
});
