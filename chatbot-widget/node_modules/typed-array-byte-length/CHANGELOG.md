# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.3](https://github.com/inspect-js/typed-array-byte-length/compare/v1.0.2...v1.0.3) - 2024-12-17

### Commits

- [types] oops, this is a type export, not a value export [`2fcf3e8`](https://github.com/inspect-js/typed-array-byte-length/commit/2fcf3e87f0312bca866dd24e805641f9c2c0798b)

## [v1.0.2](https://github.com/inspect-js/typed-array-byte-length/compare/v1.0.1...v1.0.2) - 2024-12-17

### Commits

- [types] improve types [`fcc9606`](https://github.com/inspect-js/typed-array-byte-length/commit/fcc9606bf4f27d1299aacbfa3011973ecf3f25bc)
- [types] use shared config [`ca29c46`](https://github.com/inspect-js/typed-array-byte-length/commit/ca29c46795620d624bcb6cbdbc32c1b0580ab0f8)
- [actions] split out node 10-20, and 20+ [`9a38c81`](https://github.com/inspect-js/typed-array-byte-length/commit/9a38c81be40d6e34b4a1cbe3d83dde8806a998cc)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `object-inspect`, `tape` [`78cd1f2`](https://github.com/inspect-js/typed-array-byte-length/commit/78cd1f274612d46207951cf16a05615d2d14d13e)
- [Deps] update `call-bind`, `gopd`, `has-proto`, `is-typed-array` [`8c13b84`](https://github.com/inspect-js/typed-array-byte-length/commit/8c13b84060a47865c6f6f5fafdeccfdf8f304258)
- [Tests] replace `aud` with `npm audit` [`0d9aee3`](https://github.com/inspect-js/typed-array-byte-length/commit/0d9aee379568e90c7898163b43163c3287004e71)
- [Tests] use `@arethetypeswrong/cli` [`abf28fa`](https://github.com/inspect-js/typed-array-byte-length/commit/abf28fa1baff255683c3f38fe594271178199743)
- [Dev Deps] add missing peer dep [`dfd248d`](https://github.com/inspect-js/typed-array-byte-length/commit/dfd248d58080f8c74990d76202e72acd4f3e9fa0)

## [v1.0.1](https://github.com/inspect-js/typed-array-byte-length/compare/v1.0.0...v1.0.1) - 2024-02-20

### Commits

- add types [`3144671`](https://github.com/inspect-js/typed-array-byte-length/commit/3144671ca4035136c558a107ce61af255ae3e858)
- [actions] skip ls check on node &lt; 10; remove redundant finisher [`0f83947`](https://github.com/inspect-js/typed-array-byte-length/commit/0f83947bfe641fd87253330a6a83e8b7571e5c6a)
- [Refactor] use `gopd` [`507b948`](https://github.com/inspect-js/typed-array-byte-length/commit/507b948f9e754ad5b0888a15558448ec879c04c4)
- [Dev Deps] update `aud`, `available-typed-arrays`, `npmignore`, `object-inspect`, `tape` [`aba282d`](https://github.com/inspect-js/typed-array-byte-length/commit/aba282da3a3e16f648ceb4bc4f174cf4942a94e9)
- [Deps] update `call-bind`, `has-proto`, `is-typed-array` [`acfe4a9`](https://github.com/inspect-js/typed-array-byte-length/commit/acfe4a9081f35cea3b450b07a4736e1ab037a708)
- [meta] add `sideEffects` flag [`063a8a7`](https://github.com/inspect-js/typed-array-byte-length/commit/063a8a7ec8c134a012903531beccfc4418f701d0)

## v1.0.0 - 2023-07-14

### Commits

- Initial implementation, tests, readme [`b8800c8`](https://github.com/inspect-js/typed-array-byte-length/commit/b8800c8f7f0fddd8744fd13dfa6239a504b4dc8d)
- Initial commit [`72723d8`](https://github.com/inspect-js/typed-array-byte-length/commit/72723d8f8fbff27d74b19f5e096d2eb2087d90dc)
- Only apps should have lockfiles [`a7dfc57`](https://github.com/inspect-js/typed-array-byte-length/commit/a7dfc57098655049b9c43cf1c3a39f24205821be)
