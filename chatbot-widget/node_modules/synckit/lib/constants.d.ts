import type { GlobalShim, ValueOf } from './types.js';
export declare const TsRunner: {
    readonly Node: "node";
    readonly Bun: "bun";
    readonly TsNode: "ts-node";
    readonly EsbuildRegister: "esbuild-register";
    readonly EsbuildRunner: "esbuild-runner";
    readonly OXC: "oxc";
    readonly SWC: "swc";
    readonly TSX: "tsx";
};
export type TsRunner = ValueOf<typeof TsRunner>;
export declare const TS_ESM_PARTIAL_SUPPORTED: boolean;
export declare const MTS_SUPPORTED: boolean;
export declare const MODULE_REGISTER_SUPPORTED: boolean;
export declare const STRIP_TYPES_NODE_VERSION = "22.6";
export declare const TRANSFORM_TYPES_NODE_VERSION = "22.7";
export declare const FEATURE_TYPESCRIPT_NODE_VERSION = "22.10";
export declare const DEFAULT_TYPES_NODE_VERSION = "23.6";
export declare const STRIP_TYPES_FLAG = "--experimental-strip-types";
export declare const TRANSFORM_TYPES_FLAG = "--experimental-transform-types";
export declare const NO_STRIP_TYPES_FLAG = "--no-experimental-strip-types";
export declare const NODE_OPTIONS: string[];
export declare const NO_STRIP_TYPES: boolean;
export declare const DEFAULT_TIMEOUT: number | undefined;
export declare const DEFAULT_EXEC_ARGV: string[];
export declare const DEFAULT_TS_RUNNER: TsRunner | undefined;
export declare const DEFAULT_GLOBAL_SHIMS: boolean;
export declare const DEFAULT_GLOBAL_SHIMS_PRESET: GlobalShim[];
export declare const IMPORT_FLAG = "--import";
export declare const REQUIRE_FLAG = "--require";
export declare const REQUIRE_ABBR_FLAG = "-r";
export declare const REQUIRE_FLAGS: Set<string>;
export declare const LOADER_FLAG = "--loader";
export declare const EXPERIMENTAL_LOADER_FLAG = "--experimental-loader";
export declare const LOADER_FLAGS: Set<string>;
export declare const IMPORT_FLAG_SUPPORTED: boolean;
export declare const INT32_BYTES = 4;
