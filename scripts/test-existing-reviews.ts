#!/usr/bin/env tsx

/**
 * Test script for reviews scraper using existing businesses
 * 
 * This script tests the scrapeReviewsForBusiness function using
 * the businesses that were already created in the database.
 */

import { config } from 'dotenv';
import { ApifyScraper } from '../src/lib/services/apify';
import { DatabaseService } from '../src/lib/services/database';

// Load environment variables
config({ path: '.env.local' });

// Your existing business IDs from the database
const existingBusinessIds = [
  "87a6bfe1-7fc7-4027-bbcc-336656433754", // Joe's Pizza Broadway
  "b81c2005-4362-4b90-ba5c-cb96465b69ff", // Starbucks
  "79c5c328-1c4d-4c13-872c-48f683e32791", // New York Marriott Marquis
  "345c5ac9-478f-4d4c-a2a6-cd1272a18d0d", // <PERSON>ina's
  "47830b87-2c2e-485a-8e59-3eb43c1641b3"  // <PERSON>'s
];

async function testExistingBusinessReviews() {
  console.log('🧪 Testing Reviews Scraper with Existing Businesses');
  console.log('===================================================\n');

  // Check environment variables
  const apifyToken = process.env.APIFY_API_TOKEN;
  if (!apifyToken) {
    console.error('❌ APIFY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  // Initialize services
  const scraper = new ApifyScraper(apifyToken);
  const database = new DatabaseService(true);

  try {
    // Step 1: Verify businesses exist
    console.log('🔍 Verifying existing businesses...\n');
    
    const validBusinessIds: string[] = [];
    
    for (const businessId of existingBusinessIds) {
      try {
        const business = await database.getBusinessById(businessId);
        if (business) {
          console.log(`✅ Found: ${business.business_name}`);
          console.log(`   Place ID: ${business.google_place_id}`);
          console.log(`   Total Reviews: ${business.total_reviews}`);
          console.log(`   Original Search URL: ${business.original_search_url}`);
          console.log('');
          validBusinessIds.push(businessId);
        } else {
          console.log(`❌ Business not found: ${businessId}`);
        }
      } catch (error) {
        console.error(`❌ Error checking business ${businessId}:`, error);
      }
    }

    if (validBusinessIds.length === 0) {
      console.error('❌ No valid businesses found. Cannot proceed with reviews testing.');
      process.exit(1);
    }

    console.log(`✅ Found ${validBusinessIds.length} valid businesses\n`);

    // Step 2: Test reviews scraping
    console.log('🔍 Starting reviews scraping...');
    console.log('⏳ This may take several minutes...\n');
    
    const startTime = Date.now();
    
    // Scrape reviews for all businesses (limit to 25 reviews per business for testing)
    await scraper.scrapeReviewsForBusiness(validBusinessIds, 25);
    
    const endTime = Date.now();
    const timeTaken = Math.round((endTime - startTime) / 1000);
    
    console.log(`✅ Reviews scraping completed!`);
    console.log(`   Time taken: ${timeTaken} seconds\n`);

    // Step 3: Verify reviews were saved
    console.log('🔍 Verifying saved reviews...\n');
    
    let totalReviews = 0;
    let businessesWithReviews = 0;
    
    for (const businessId of validBusinessIds) {
      try {
        // Get business details
        const business = await database.getBusinessById(businessId);
        if (!business) {
          console.log(`❌ Business not found: ${businessId}`);
          continue;
        }
        
        console.log(`📊 ${business.business_name}`);
        console.log(`   Place ID: ${business.google_place_id}`);
        
        // Get reviews for this business
        const reviews = await database.getRecentReviews(business.google_place_id, 100);
        
        console.log(`   Reviews scraped: ${reviews.length}`);
        
        if (reviews.length > 0) {
          businessesWithReviews++;
          totalReviews += reviews.length;
          
          // Show sample review
          const sampleReview = reviews[0];
          console.log(`   📝 Sample review:`);
          console.log(`      Author: ${sampleReview.author_name || 'Anonymous'}`);
          console.log(`      Rating: ${sampleReview.rating}/5`);
          console.log(`      Text: ${(sampleReview.review_text || '').substring(0, 80)}...`);
          console.log(`      Date: ${sampleReview.review_date}`);
          
          // Show review distribution
          const ratingCounts = reviews.reduce((acc, review) => {
            const rating = review.rating || 0;
            acc[rating] = (acc[rating] || 0) + 1;
            return acc;
          }, {} as Record<number, number>);
          
          console.log(`   ⭐ Rating distribution: ${Object.entries(ratingCounts)
            .sort(([a], [b]) => Number(b) - Number(a))
            .map(([rating, count]) => `${rating}★(${count})`)
            .join(', ')}`);
          
        } else {
          console.log(`   ⚠️  No reviews found for this business`);
        }
        
        console.log(''); // Empty line for readability
        
      } catch (error) {
        console.error(`❌ Error verifying business ${businessId}:`, error);
      }
    }

    // Step 4: Summary
    console.log('📊 Test Summary');
    console.log('================');
    console.log(`🏢 Businesses tested: ${validBusinessIds.length}`);
    console.log(`📝 Businesses with reviews: ${businessesWithReviews}`);
    console.log(`📊 Total reviews scraped: ${totalReviews}`);
    console.log(`📈 Average reviews per business: ${validBusinessIds.length > 0 ? (totalReviews / validBusinessIds.length).toFixed(1) : 0}`);
    console.log(`✅ Success rate: ${((businessesWithReviews / validBusinessIds.length) * 100).toFixed(1)}%`);
    
    if (businessesWithReviews === validBusinessIds.length) {
      console.log('\n🎉 All businesses have reviews! Reviews scraper is working correctly.');
    } else if (businessesWithReviews > 0) {
      console.log('\n⚠️  Some businesses have no reviews. This might be due to API limitations or rate limiting.');
    } else {
      console.log('\n❌ No reviews were scraped. Please check the Apify configuration and API limits.');
    }

    // Step 5: Show database stats
    console.log('\n📈 Database Statistics');
    console.log('======================');
    
    try {
      // This is a simple way to get total counts - in production you'd want proper aggregation queries
      let totalBusinesses = 0;
      let totalAllReviews = 0;
      
      for (const businessId of validBusinessIds) {
        const business = await database.getBusinessById(businessId);
        if (business) {
          totalBusinesses++;
          const reviews = await database.getRecentReviews(business.google_place_id, 1000);
          totalAllReviews += reviews.length;
        }
      }
      
      console.log(`📊 Total businesses in test: ${totalBusinesses}`);
      console.log(`📝 Total reviews in database: ${totalAllReviews}`);
      console.log(`💾 Average reviews per business: ${totalBusinesses > 0 ? (totalAllReviews / totalBusinesses).toFixed(1) : 0}`);
      
    } catch (error) {
      console.error('❌ Error getting database statistics:', error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testExistingBusinessReviews();
}

export { testExistingBusinessReviews };
